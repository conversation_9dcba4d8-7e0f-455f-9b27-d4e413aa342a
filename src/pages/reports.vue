<template>
  <q-page class="q-pa-lg ">
    <div class="q-gutter-y-lg">
      <div class="column q-gutter-md">
        <div class="row items-center">
          <span class="q-ml-lg col-1"> {{ $t("reportType") }}: </span>
          <q-select
            class="q-ml-md"
            outlined
            dense
            clearable
            v-model="selectedReport"
            :options="reportsOptions"
            :option-label="
              opt =>
                Object(opt) === opt && 'key' in opt
                  ? opt.key + '. ' + opt.value
                  : ''
            "
            option-value="key"
            emit-value
            map-options
            style="min-width: 250px;"
          />
        </div>

        <div class="row items-center">
          <span class="q-ml-lg col-1"> {{ $t("datefrom") }}: </span>
          <q-input
            class="q-ml-md"
            outlined
            dense
            v-model="reportDateFrom"
            mask="date"
          >
            <template v-slot:append>
              <q-icon name="event" class="cursor-pointer">
                <q-popup-proxy
                  ref="qDateProxy"
                  cover
                  transition-show="scale"
                  transition-hide="scale"
                >
                  <q-date v-model="reportDateFrom" :locale="myLocale">
                    <div class="row items-center justify-end">
                      <q-btn v-close-popup :label="$t('close')" color="primary" flat />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </div>

        <div class="row items-center">
          <span class="q-ml-lg col-1"> {{ $t("dateto") }}: </span>
          <q-input
            class="q-ml-md"
            dense
            outlined
            v-model="reportDateTo"
            mask="date"
          >
            <template v-slot:append>
              <q-icon name="event" class="cursor-pointer">
                <q-popup-proxy
                  ref="qDateProxy"
                  cover
                  transition-show="scale"
                  transition-hide="scale"
                >
                  <q-date v-model="reportDateTo"  :locale="myLocale">
                    <div class="row items-center justify-end">
                      <q-btn v-close-popup :label="$t('close')"  color="primary" flat />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </div>
      </div>

      <h4
        style="font-size: 21px; text-align: center; marginTop: 50px; font-weight: 500;"
        v-if="allThreeSelected"
      >
        {{ reportTitle }}
      </h4>

      <!-- 1 report -->
      <div
        class="column tablecontainer"
        v-if="allThreeSelected && selectedReport == 1"
      >
        <div class="row">
          <div class="col-2" style="background: darkgrey;"></div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("registration") }}
          </div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("authorization") }}
          </div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("finOper") }}
          </div>
        </div>
        <div class="row">
          <div class="column col-2" style="background: darkgrey;"></div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{$t('ul')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
               {{$t('fl')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{$t('ep')}}
            </div>
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
                            {{$t('ul')}}

            </div>
            <div class="col row items-center justify-center rowstyle">
               {{$t('fl')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{$t('ep')}}
            </div>
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
                            {{$t('ul')}}

            </div>
            <div class="col row items-center justify-center rowstyle">
               {{$t('fl')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{$t('ep')}}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ $t("eventsAmount") }}
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{
                report1 && report1.events ? report1.events.registration_ul : ""
              }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{
                report1 && report1.events ? report1.events.registration_fl : ""
              }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{
                report1 && report1.events ? report1.events.registration_ip : ""
              }}
            </div>
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
              {{ report1 && report1.events ? report1.events.auth_ul : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report1 && report1.events ? report1.events.auth_fl : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report1 && report1.events ? report1.events.auth_ip : "" }}
            </div>
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ report1 && report1.events ? report1.events.fin_oper_ul : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report1 && report1.events ? report1.events.fin_oper_fl : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report1 && report1.events ? report1.events.fin_oper_ip : "" }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ $t("caseCount") }}
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{
                report1 && report1.cases ? report1.cases.registration_ul : ""
              }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{
                report1 && report1.cases ? report1.cases.registration_fl : ""
              }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{
                report1 && report1.cases ? report1.cases.registration_ip : ""
              }}
            </div>
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
              {{ report1 && report1.cases ? report1.cases.auth_ul : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report1 && report1.cases ? report1.cases.auth_fl : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report1 && report1.cases ? report1.cases.auth_ip : "" }}
            </div>
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ report1 && report1.cases ? report1.cases.fin_oper_ul : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report1 && report1.cases ? report1.cases.fin_oper_fl : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report1 && report1.cases ? report1.cases.fin_oper_ip : "" }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="allThreeSelected && selectedReport == 1">
        <Bar :chartData="report1datasets" :options="report1options" />
      </div>

      <!-- 2 report -->
      <div
        class="column tablecontainer"
        v-if="allThreeSelected && selectedReport == 2"
      >
        <div class="row">
          <div class="col-2" style="background: darkgrey;"></div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("eventsAmount") }}
          </div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("caseCount") }}
          </div>
        </div>
        <div class="row">
          <div class="column col-2" style="background: darkgrey;"></div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ $t("registration") }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ $t("authorization") }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ $t("finOperShort") }}
            </div>
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
              {{ $t("registration") }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ $t("authorization") }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ $t("finOperShort") }}
            </div>
          </div>
        </div>
        <div class="row" v-for="(row, index) in report3.cases" :key="index">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ row.country_name }}
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ report3.events[index].registration }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report3.events[index].auth }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report3.events[index].fin_oper }}
            </div>
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
              {{ row.registration }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ row.auth }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ row.fin_oper }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="allThreeSelected && selectedReport == 2">
        <Bar :chartData="report2datasets" :options="report1options" />
      </div>

      <!-- 3 report -->
      <div
        class="column tablecontainer"
        v-if="allThreeSelected && selectedReport == 3"
      >
        <div class="row">
          <div class="column col-2" style="background: darkgrey;"></div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              Post.kz (web)
            </div>
            <div class="col row items-center justify-center rowstyle">
              Post.kz (mobile)
            </div>
            <div class="col row items-center justify-center rowstyle">
              PayPost
            </div>
            <div class="col row items-center justify-center rowstyle">
              Way4
            </div>
            <div class="col row items-center justify-center rowstyle">
              Colvir
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ $t("eventsAmount") }}
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{
                report4 && report4.events ? report4.events[0].post_kz_web : ""
              }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{
                report4 && report4.events
                  ? report4.events[0].post_kz_mobile
                  : ""
              }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report4 && report4.events ? report4.events[0].pay_post : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report4 && report4.events ? report4.events[0].way_4 : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report4 && report4.events ? report4.events[0].colvir : "" }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ $t("caseCount") }}
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ report4 && report4.cases ? report4.cases[0].post_kz_web : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{
                report4 && report4.cases ? report4.cases[0].post_kz_mobile : ""
              }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report4 && report4.cases ? report4.cases[0].pay_post : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report4 && report4.cases ? report4.cases[0].way_4 : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report4 && report4.cases ? report4.cases[0].colvir : "" }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="allThreeSelected && selectedReport == 3">
        <Bar :chartData="report3datasets" :options="report1options" />
      </div>

      <!-- 4 report -->
      <div
        class="column tablecontainer"
        v-if="allThreeSelected && selectedReport == 4"
        style="box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);"
      >
        <div class="row">
          <div class="col-2" style="background: darkgrey;"></div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("eventsAmount") }}
          </div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("caseCount") }}
          </div>
        </div>
        <div class="row">
          <div class="column col-2" style="background: darkgrey;"></div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
                            {{$t('ul')}}

            </div>
            <div class="col row items-center justify-center rowstyle">
               {{$t('fl')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{$t('ep')}}
            </div>
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
                            {{$t('ul')}}

            </div>
            <div class="col row items-center justify-center rowstyle">
               {{$t('fl')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{$t('ep')}}
            </div>
          </div>
        </div>
        <div class="row" v-for="(row, index) in report2.cases" :key="index">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ currLang == 'ru' ? report2.events[index].country_name : report2.events[index].country_name_kz }}
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
              {{ report2.events[index].ul }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report2.events[index].fl }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report2.events[index].ip }}
            </div>
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ row.ul }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ row.fl }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ row.ip }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="allThreeSelected && selectedReport == 4">
        <Bar :chartData="report4datasets" :options="report1options" />
      </div>

      <!-- 5 report -->
      <div
        class="column tablecontainer"
        v-if="allThreeSelected && selectedReport == 5"
      >
        <div class="row">
          <div class="col-2" style="background: darkgrey;"></div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("registration") }}
          </div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("authorization") }}
          </div>
        </div>
        <div class="row">
          <div class="column col-2" style="background: darkgrey;"></div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
                            {{$t('ul')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
               {{$t('fl')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{$t('ep')}}
            </div>
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
                            {{$t('ul')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
               {{$t('fl')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{$t('ep')}}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ $t("allowed") }}
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
              {{ report5.registration_ul_allowed }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report5.registration_fl_allowed }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report5.registration_ip_allowed }}
            </div>
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ report5.auth_ul_allowed }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report5.auth_fl_allowed }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report5.auth_ip_allowed }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ $t("suspended") }}
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
              {{ report5.registration_ul_suspended }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report5.registration_fl_suspended }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report5.registration_ip_suspended }}
            </div>
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ report5.auth_ul_suspended }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report5.auth_fl_suspended }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report5.auth_ip_suspended }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ $t("forbidden") }}
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
              {{ report5.registration_ul_forbidden }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report5.registration_fl_forbidden }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report5.registration_ip_forbidden }}
            </div>
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ report5.auth_ul_forbidden }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report5.auth_fl_forbidden }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report5.auth_ip_forbidden }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="allThreeSelected && selectedReport == 5">
        <Bar :chartData="report5datasets" :options="report1options" />
      </div>

      <!-- 6 report -->
      <div
        class="column tablecontainer"
        v-if="allThreeSelected && selectedReport == 6"
      >
        <div class="row">
          <div class="col-2" style="background: darkgrey;"></div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("eventsAmount") }}
          </div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("caseCount") }}
          </div>
        </div>
        <div class="row">
          <div class="column col-2" style="background: darkgrey;"></div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
                            {{$t('ul')}}

            </div>
            <div class="col row items-center justify-center rowstyle">
               {{$t('fl')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{$t('ep')}}
            </div>
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
                            {{$t('ul')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
               {{$t('fl')}}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{$t('ep')}}
            </div>
          </div>
        </div>
        <div class="row" v-for="(row, index) in report6.cases" :key="index">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ row.product_id }}
          </div>
          <div class="col row ">
            <div class="col row items-center justify-center rowstyle">
              {{ report6.events && report6.events[index] ? report6.events[index].fin_oper_ul : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report6.events && report6.events[index] ? report6.events[index].fin_oper_fl : "" }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report6.events && report6.events[index] ? report6.events[index].fin_oper_ip : "" }}
            </div>
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ row.fin_oper_ul }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ row.fin_oper_fl }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ row.fin_oper_ip }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="allThreeSelected && selectedReport == 6">
        <Bar :chartData="report6datasets" :options="report1options" />
      </div>

      <!-- 7 report -->
      <div
        class="column tablecontainer"
        v-if="allThreeSelected && selectedReport == 7"
      >
        <div class="row">
          <div class="col-2" style="background: darkgrey;"></div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("eventsAmount") }}
          </div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("caseCount") }}
          </div>
        </div>
        <div class="row" v-if="report7 && report7.events">
          <div class="column col-2" style="background: darkgrey;"></div>
          <div
            class="col column scroller"
            style=" max-height: 40px; overflow-x: scroll;  overflow-y: hidden"
            @scroll="e => handleOnScrollHeader(e, '#rowfirstpart')"
          >
            <div
              v-for="(elem, propertyName, index) in report7.events[0]"
              style="min-width: 40px;"
              :key="index"
              v-show="propertyName !== 'product_id'"
              class="row items-center justify-center rowstyle"
            >
              {{ propertyName !== "product_id" ? propertyName : "" }}
            </div>
          </div>
          <div
            class="col column scroller"
            style=" max-height: 40px; overflow-x: scroll;  overflow-y: hidden"
            @scroll="e => handleOnScrollHeader(e, '#rowsecondpart')"
          >
            <div
              v-for="(elem, propertyName, index) in report7.events[0]"
              style="min-width: 40px;"
              :key="index"
              v-show="propertyName !== 'product_id'"
              class="row items-center justify-center rowstyle"
            >
              {{ propertyName !== "product_id" ? propertyName : "" }}
            </div>
          </div>
        </div>
        <div class="row" v-for="(row, index) in report7.cases" :key="index">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ row.product_id }}
          </div>
          <div
            class="col column"
            style=" max-height: 40px; overflow-x: hidden;  overflow-y: hidden; "
            id="rowfirstpart"
          >
            <div
              v-for="(elem, propertyName, index) in report7.events[index]"
              style="min-width: 40px"
              :key="index"
              v-show="propertyName !== 'product_id'"
              class="row items-center justify-center rowstyle"
            >
              {{ propertyName !== "product_id" ? elem : "" }}
            </div>
          </div>
          <div
            class="col column"
            style=" max-height: 40px; overflow-x: hidden;  overflow-y: hidden;"
            id="rowsecondpart"
          >
            <div
              v-for="(elem, propertyName, index) in report7.cases[index]"
              style="min-width: 40px"
              :key="index"
              v-show="propertyName !== 'product_id'"
              class="row items-center justify-center rowstyle"
            >
              {{ propertyName !== "product_id" ? elem : "" }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="allThreeSelected && selectedReport == 7">
        <Bar :chartData="report7datasets" :options="report1options" />
      </div>

      <!-- 8 report -->
      <div
        class="column tablecontainer"
        v-if="allThreeSelected && selectedReport == 8"
      >
        <div class="row">
          <div class="col-2" style="background: darkgrey;"></div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("eventsAmount") }}
          </div>
          <div class="col row items-center justify-center rowstyle">
            {{ $t("caseCount") }}
          </div>
        </div>
        <div class="row" v-if="report8 && report8.events">
          <div class="column col-2" style="background: darkgrey;"></div>
          <div
            class="col column scroller"
            style=" max-height: 40px; overflow-x: scroll; overflow-y: hidden "
            @scroll="e => handleOnScrollHeader(e, '#rowthirdpart')"
          >
            <div
              v-for="(elem, propertyName, index) in report8.events[0]"
              style="min-width: 150px;"
              :key="index"
              v-show="propertyName !== 'country_name' && propertyName !== 'country_name_kz' && propertyName !== 'country_name_en'"
              class="row items-center justify-center rowstyle"
            >
              {{
                propertyName !== "country_name" && propertyName !== "string" && propertyName !== 'country_name_kz' && propertyName !== 'country_name_en'
                  ? propertyName
                  : ""
              }}
            </div>
          </div>
          <div
            class="col column scroller"
            style=" max-height: 40px; overflow-x: scroll; overflow-y: hidden; "
            @scroll="e => handleOnScrollHeader(e, '#rowfourthpart')"
          >
            <div
              v-for="(elem, propertyName, index) in report8.events[0]"
              style="min-width: 150px;"
              :key="index"
              v-show="propertyName !== 'country_name' && propertyName !== 'country_name_kz' && propertyName !== 'country_name_en'"
              class="row items-center justify-center rowstyle"
            >
              {{
                propertyName !== "country_name" && propertyName !== 'country_name_kz' && propertyName !== 'country_name_en' && propertyName !== "string"
                  ? propertyName
                  : ""
              }}
            </div>
          </div>
        </div>
        <div class="row" v-for="(row, index) in report8.cases" :key="index">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ currLang == 'ru' ? report8.events[index].country_name : report8.events[index].country_name_kz }}
          </div>
          <div
            class="col column"
            style=" max-height: 40px; overflow-x: hidden; overflow-y: hidden; "
            id="rowthirdpart"
          >
            <div
              v-for="(elem, propertyName, index) in report8.events[index]"
              style="min-width: 150px"
              :key="index"
              v-show="propertyName !== 'country_name' && propertyName !== 'country_name_kz' && propertyName !== 'country_name_en'"
              class="row items-center justify-center rowstyle"
            >
              {{
                propertyName !== "country_name" && propertyName !== "string" && propertyName !== 'country_name_kz' && propertyName !== 'country_name_en'
                  ? elem
                  : ""
              }}
            </div>
          </div>
          <div
            class="col column"
            style=" max-height: 40px; overflow-x: hidden; overflow-y: hidden; "
            id="rowfourthpart"
          >
            <div
              v-for="(elem, propertyName, index) in report8.cases[index]"
              style="min-width: 150px"
              :key="index"
              v-show="propertyName !== 'country_name' && propertyName !== 'country_name_kz' && propertyName !== 'country_name_en' "
              class="row items-center justify-center rowstyle"
            >
              {{
                propertyName !== "country_name" && propertyName !== "string" && propertyName !== 'country_name_kz' && propertyName !== 'country_name_en'
                  ? elem
                  : ""
              }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="allThreeSelected && selectedReport == 8">
        <Bar :chartData="report8datasets" :options="report1options" />
      </div>

      <!-- 9 report -->
      <div
        class="column tablecontainer"
        v-if="allThreeSelected && selectedReport == 9"
      >
        <div class="row">
          <div class="column col-2" style="background: darkgrey;"></div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ $t("allowed") }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ $t("suspended") }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ $t("forbidden") }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ $t("authorization") }}
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ report9 && report9.auth_allowed }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report9 && report9.auth_suspended }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report9 && report9.auth_forbidden }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ $t("registration") }}
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ report9 && report9.registration_allowed }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report9 && report9.registration_suspended }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report9 && report9.registration_forbidden }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-2 row items-center justify-center rowstyle">
            {{ $t("finOperShort") }}
          </div>
          <div class="col row">
            <div class="col row items-center justify-center rowstyle">
              {{ report9 && report9.fin_oper_allowed }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report9 && report9.fin_oper_suspended }}
            </div>
            <div class="col row items-center justify-center rowstyle">
              {{ report9 && report9.fin_oper_forbidden }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="allThreeSelected && selectedReport == 9">
        <Bar :chartData="report9datasets" :options="report1options" />
      </div>

      <CustomTable
        v-if="allThreeSelected && selectedReport == 10"
        :data="report10"
        :columns="columns"
        :tabletitle="$t('report10')"
        :pagination.sync="pagination"
        @onRequest="request"
        :isDeleteAvailable="false"
        :isAddAvailable="false"
        :isEditAvailable="false"
        :loading="loading"
        :filter.sync="filter"
      >
        <template v-slot:case_date="{ props }">
          <q-item>
            <q-item-section>
              <q-item-label>
                {{
                  props.row.case_date
                    ? formatDateDefault(props.row.case_date)
                    : ""
                }}
              </q-item-label>
            </q-item-section>
          </q-item>
        </template>
      </CustomTable>

      <CustomTable
        v-if="allThreeSelected && selectedReport == 11"
        :data="way4cardfrequency"
        :columns="way4cardFrequencyColumns"
        :tabletitle="$t('report11')"
        :pagination.sync="paginationway4CardFrequency"
        :isDeleteAvailable="false"
        :isAddAvailable="false"
        :isEditAvailable="false"
        :loading="loadingCardFrequency"
        :filter.sync="filterWay4CardFrequency"
      >
        <template v-slot:searchfield="{ props }">
          <q-input
            outlined
            dense
            debounce="300"
            v-model="filterWay4AllTransactions"
            :placeholder="$t('search')"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
        </template>
      </CustomTable>

      <CustomTable
        v-if="allThreeSelected && selectedReport == 12"
        :data="way4cardoperationfrequency"
        :columns="way4cardOperationFrequencyColumns"
        :tabletitle="$t('report12')"
        :pagination.sync="paginationway4CardOperationFrequency"
        :isDeleteAvailable="false"
        :isAddAvailable="false"
        :isEditAvailable="false"
        :loading="loadingCardOperationFrequency"
        :filter.sync="filterWay4CardOperationFrequency"
      >
        <template v-slot:searchfield="{ props }">
          <q-input
            outlined
            dense
            debounce="300"
            v-model="filterWay4CardOperationFrequency"
            :placeholder="$t('search')"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
        </template>
      </CustomTable>

      <CustomTable
        v-if="allThreeSelected && selectedReport == 13"
        :data="way4alltransactions"
        :columns="way4AllTransactionsColumns"
        :tabletitle="$t('report13')"
        :pagination.sync="paginationway4AllTransactions"
        :isDeleteAvailable="false"
        :isAddAvailable="false"
        :isEditAvailable="false"
        :loading="loadingWay4AllTransactions"
        :filter.sync="filterWay4AllTransactions"
      >
        <template v-slot:searchfield="{ props }">
          <q-input
            outlined
            dense
            debounce="300"
            v-model="filterWay4AllTransactions"
            :placeholder="$t('search')"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
        </template>
      </CustomTable>

      <CustomTable
        v-if="allThreeSelected && selectedReport == 14"
        :data="way4alltransactions"
        :columns="way4BigSumColumns"
        :tabletitle="$t('report14')"
        :pagination.sync="paginationway4BigSum"
        :isDeleteAvailable="false"
        :isAddAvailable="false"
        :isEditAvailable="false"
        :loading="loadingWay4AllTransactions"
        :filter.sync="filterWay4BigSum"
      >
        <template v-slot:searchfield="{ props }">
          <q-input
            outlined
            dense
            debounce="300"
            v-model="filterWay4BigSum"
            :placeholder="$t('search')"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
        </template>
      </CustomTable>
    </div>
  </q-page>
</template>

<script>
import { mapState, mapActions } from "vuex";
import datemixin from "../mixins/datemixin";
import Bar from "../components/BarChart";

export default {
  mixins: [datemixin],
  components: {
    CustomTable: () => import("../components/CustomTable"),
    Bar
  },
  data() {
    return {
       
      loading: false,
      loadingWay4AllTransactions: false,
      loadingCardFrequency: false,
      loadingCardOperationFrequency: false,
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },

      paginationway4AllTransactions: {
        sortBy: "desc",
        descending: false,
        page: 1,
        rowsPerPage: 5
      },

      paginationway4BigSum: {
        sortBy: "desc",
        descending: false,
        page: 1,
        rowsPerPage: 5
      },
      paginationway4CardFrequency: {
        sortBy: "desc",
        descending: false,
        page: 1,
        rowsPerPage: 5
      },
      paginationway4CardOperationFrequency: {
        sortBy: "desc",
        descending: false,
        page: 1,
        rowsPerPage: 5
      },
      filter: "",
      filterWay4AllTransactions: "",
      filterWay4BigSum: "",
      filterWay4CardFrequency: "",
      filterWay4CardOperationFrequency: "",
      report1options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          xAxes: [
            {
              stacked: true,
              categoryPercentage: 0.5,
              barPercentage: 1
            }
          ],
          yAxes: [
            {
              stacked: true
            }
          ]
        }
      },
      selectedReport: "",
      reportDateFrom: "",
      reportDateTo: ""
    };
  },

  created() {
    const obj = { pagination: this.pagination, filter: this.filter };
    this.request(obj);
  },

  methods: {
    ...mapActions({
      getReport1: "admin/getReport1",
      getReport2: "admin/getReport2",
      getReport3: "admin/getReport3",
      getReport4: "admin/getReport4",
      getReport5: "admin/getReport5",
      getReport6: "admin/getReport6",
      getReport7: "admin/getReport7",
      getReport8: "admin/getReport8",
      getReport9: "admin/getReport9",
      getReport10: "admin/getReport10",
      getWay4AllTransactions: "admin/getWay4AllTransactions",
      getWay4CardFrequency: "admin/getWay4CardFrequency",
      getWay4CardOperationFrequency: "admin/getWay4CardOperationFrequency",

      async request(props) {
        if (!props.pagination) {
          return;
        }
        const {
          page,
          rowsPerPage,
          rowsNumber,
          sortBy,
          descending
        } = props.pagination;
        const filter = props.filter;
        let start_date = this.formatDateToDB(this.reportDateFrom);
        let end_date = this.formatDateToDB(this.reportDateTo);

        this.loading = true;

        // get all rows if "All" (0) is selected
        const fetchCount =
          rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

        // calculate starting row of data
        const startRow = (page - 1) * rowsPerPage;

        this.pagination.rowsNumber = await this.getReport10({
          page: page,
          per_page: rowsPerPage,
          sort_order: descending ? "desc" : "asc",
          filter_fields: encodeURIComponent("client_id"),
          filter_values: filter,
          start_date,
          end_date
        });

        // don't forget to update local pagination object
        this.pagination.page = page;
        this.pagination.rowsPerPage = rowsPerPage;
        this.pagination.sortBy = sortBy;
        this.pagination.descending = descending;
        this.loading = false;
      },

      fetchReport() {
        let start_date = this.formatDateToDB(this.reportDateFrom);
        let end_date = this.formatDateToDB(this.reportDateTo);
        let num = this.selectedReport;
        if (num == 1) {
          this.getReport1({ start_date, end_date })
            .then()
            .catch(err => {
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
        } else if (num == 2) {
          this.getReport3({ start_date, end_date })
            .then()
            .catch(err => {
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
        } else if (num == 3) {
          this.getReport4({ start_date, end_date })
            .then()
            .catch(err => {
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
        } else if (num == 4) {
          this.getReport2({ start_date, end_date })
            .then()
            .catch(err => {
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
        } else if (num == 5) {
          this.getReport5({ start_date, end_date })
            .then()
            .catch(err => {
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
        } else if (num == 6) {
          this.getReport6({ start_date, end_date })
            .then()
            .catch(err => {
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
        } else if (num == 7) {
          this.getReport7({ start_date, end_date })
            .then()
            .catch(err => {
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
        } else if (num == 8) {
          this.getReport8({ start_date, end_date })
            .then()
            .catch(err => {
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
        } else if (num == 9) {
          this.getReport9({ start_date, end_date })
            .then()
            .catch(err => {
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
        } else if (num == 11) {
          start_date = this.formatWay4(this.reportDateFrom);
          end_date = this.formatWay4(this.reportDateTo);
          this.loadingCardFrequency = true;
          this.getWay4CardFrequency({ start_date, end_date })
            .then(() => {
              this.loadingCardFrequency = false;
            })
            .catch(err => {
              this.loadingCardFrequency = false;
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
        } else if (num == 12) {
          start_date = this.formatWay4(this.reportDateFrom);
          end_date = this.formatWay4(this.reportDateTo);
          this.loadingCardOperationFrequency = true;
          this.getWay4CardOperationFrequency({ start_date, end_date })
            .then(() => {
              this.loadingCardOperationFrequency = false;
            })
            .catch(err => {
              this.loadingCardOperationFrequency = false;
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
        } else if (num == 13 || num == 14) {
          start_date = this.formatWay4(this.reportDateFrom);
          end_date = this.formatWay4(this.reportDateTo);
          this.loadingWay4AllTransactions = true;
          this.getWay4AllTransactions({ start_date, end_date })
            .then(() => {
              this.loadingWay4AllTransactions = false;
            })
            .catch(err => {
              this.loadingWay4AllTransactions = false;
              this.$q.notify({
                message: err.error,
                color: "negative"
              });
            });
        }
      }
    }),

    handleOnScrollHeader(e, t) {
      let myNodeList = document.querySelectorAll(t);

      for (var i = 0; i < myNodeList.length; ++i) {
        var element = myNodeList[i]; // Вызов myNodeList.item(i) необязателен в JavaScript
        element.scrollLeft = e.target.scrollLeft;
      }
    }
  },
  computed: mapState({
    report1: state => state.admin.report1,
    report2: state => state.admin.report2,
    report3: state => state.admin.report3,
    report4: state => state.admin.report4,
    report5: state => state.admin.report5,
    report6: state => state.admin.report6,
    report7: state => state.admin.report7,
    report8: state => state.admin.report8,
    report9: state => state.admin.report9,
    report10: state => state.admin.report10,
    way4cardfrequency: state => state.admin.way4cardfrequency,
    way4alltransactions: state => state.admin.way4alltransactions,
    way4cardoperationfrequency: state => state.admin.way4cardoperationfrequency,

    myLocale() {
      return {
        /* starting with Sunday */
          days: [
          this.$t("sunday"),
          this.$t("monday"),
          this.$t("tuesday"),
          this.$t("wednesday"),
          this.$t("thursday"),
          this.$t("friday"),
          this.$t('saturday')
        ],
        daysShort: [
          this.$t("sundayShort"),
          this.$t("mondayShort"),
          this.$t("tuesdayShort"),
          this.$t("wednesdayShort"),
          this.$t("thursdayShort"),
          this.$t("fridayShort"),
          this.$t('saturdayShort')
        ],
        months: [
          this.$t("january"),
          this.$t("february"),
          this.$t("march"),
          this.$t("april"),
          this.$t("may"),
          this.$t("june"),
          this.$t("july"),
          this.$t("august"),
          this.$t("september"),
          this.$t("october"),
          this.$t("november"),
          this.$t("december")
        ],
        monthsShort: [
          this.$t("janShort"),
          this.$t("febShort"),
          this.$t("marchShort"),
          this.$t("aprilShort"),
          this.$t("mayShort"),
          this.$t("juneShort"),
          this.$t("julyShort"),
          this.$t("augustShort"),
          this.$t("septemberShort"),
          this.$t("octoberShort"),
          this.$t("novemberShort"),
          this.$t("decemberShort")
        ],
        firstDayOfWeek: 1
      }
    },

    way4BigSumColumns() {
      return [
        {
          name: "AmndDate",
          label: this.$t("downloadDate"),
          field: row => row.AmndDate,
          sortable: true,
          align: "center"
        },
        {
          name: "RetRefNumber",
          label: "PPH",
          field: row => row.RetRefNumber,
          align: "center"
        },
        {
          name: "TransDate",
          label: this.$t("transactionDate"),
          field: row => row.TransDate,
          align: "center"
        },
        {
          name: "TransAmount",
          label: this.$t("transactionAmount"),
          field: row => row.TransAmount,
          align: "center"
        },
        {
          name: "TransCurr",
          label: this.$t("currency"),
          field: row => row.TransCurr,
          align: "center"
        },
        {
          name: "MerchantId",
          label: this.$t("merchantName"),
          field: row => row.MerchantId,
          align: "center"
        },
        {
          name: "TransDetails",
          label: this.$t("transactionDetails"),
          field: row => row.TransDetails,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ];
    },

    way4AllTransactionsColumns() {
      return [
        {
          name: "AmndDate",
          label: this.$t("downloadDate"),
          field: row => row.AmndDate,
          sortable: true,
          align: "center"
        },
        {
          name: "RetRefNumber",
          label: "PPH",
          field: row => row.RetRefNumber,
          align: "center"
        },
        {
          name: "Card",
          label: this.$t("cardNumber"),
          field: row => row.Card,
          align: "center"
        },
        {
          name: "TargetMemberId",
          label: this.$t("userIdentification"),
          field: row => row.TargetMemberId,
          align: "center"
        },
        {
          name: "SourceNumber",
          label: this.$t("sourceNumber"),
          field: row => row.SourceNumber,
          align: "center"
        },
        {
          name: "AuthCode",
          label: this.$t("authorizationCode"),
          field: row => row.AuthCode,
          align: "center"
        },
        {
          name: "TransCountry",
          label: this.$t("transactionCountry"),
          field: row => row.TransCountry,
          align: "center"
        },
        {
          name: "TransCity",
          label: this.$t("transactionCity"),
          field: row => row.TransCity,
          align: "center"
        },
        {
          name: "TransDetails",
          label: this.$t("transactionDetails"),
          field: row => row.TransDetails,
          align: "center"
        },
        {
          name: "TransDate",
          label: this.$t("transactionDate"),
          field: row => row.TransDate,
          align: "center"
        },
        {
          name: "TransAmount",
          label: this.$t("transactionAmount"),
          field: row => row.TransAmount,
          align: "center"
        },
        {
          name: "SIC",
          label: "SIC Code",
          field: row => row.SIC,
          align: "center"
        },
        {
          name: "PostingDate",
          label: this.$t("transactionStatus"),
          field: row => row.PostingDate,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ];
    },

    way4cardFrequencyColumns() {
      return [
        {
          name: "operation_type",
          label: this.$t("operationType"),
          field: row => row.operation_type,
          sortable: true,
          align: "center"
        },
        {
          name: "our_cards",
          label: this.$t("ourCardsAtUs"),
          field: row => row.our_cards,
          align: "center"
        },
        {
          name: "our_cards_in_foreign",
          label: this.$t("ourCardsDifferentNetwork"),
          field: row => row.our_cards_in_foreign,
          align: "center"
        },
        {
          name: "bvu_cards",
          label: this.$t("bvuCardsOurs"),
          field: row => row.bvu_cards,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ];
    },

    way4cardOperationFrequencyColumns() {
      return [
        {
          name: "card_mask",
          label: this.$t("cardNumber"),
          field: row => row.card_mask,
          sortable: true,
          align: "center"
        },
        {
          name: "operation_type",
          label: this.$t("operationType"),
          field: row => row.operation_type,
          align: "center"
        },
        {
          name: "operation_frequency",
          label: this.$t("operationFrequency"),
          field: row => row.operation_frequency,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ];
    },

    columns() {
      return [
        {
          name: "client_id",
          label: this.$t("clientId"),
          field: row => row.client_id,
          sortable: true,
          align: "center"
        },
        {
          name: "client_name",
          label: this.$t("fullName"),
          field: row => row.client_name,
          align: "center"
        },
        {
          name: "scenario_name",
          label: this.$t("scenarioName"),
          field: row => row.scenario_name,
          align: "center"
        },
        {
          name: "case_date",
          label: this.$t("caseCreationDate"),
          field: row => row.case_date,
          align: "center"
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ];
    },

    reportsOptions() {
      return [
        {
          key: 1,
          value: this.$t("report1")
        },
        {
          key: 2,
          value: this.$t("report2")
        },
        {
          key: 3,
          value: this.$t("report3")
        },
        {
          key: 4,
          value: this.$t("report4")
        },
        {
          key: 5,
          value: this.$t("report5")
        },
        {
          key: 6,
          value: this.$t("report6")
        },
        {
          key: 7,
          value: this.$t("report7")
        },
        {
          key: 8,
          value: this.$t("report8")
        },
        {
          key: 9,
          value: this.$t("report9")
        },
        {
          key: 10,
          value: this.$t("report10")
        },
        {
          key: 11,
          value: this.$t("report11")
        },
        {
          key: 12,
          value: this.$t("report12")
        },
        {
          key: 13,
          value: this.$t("report13")
        },
        {
          key: 14,
          value: this.$t("report14")
        }
      ];
    },

    report1datasets() {
      const report1labels = [
          `${this.$t("registration")} ${this.$t("ul")}`,
        `${this.$t("registration")} ${this.$t("fl")}`,
        `${this.$t("registration")} ${this.$t("ep")}`,
         `${this.$t("authorization")} ${this.$t("ul")}`,
        `${this.$t("authorization")} ${this.$t("fl")}`,
        `${this.$t("authorization")} ${this.$t("ep")}`,
         `${this.$t("finOperShort")} ${this.$t("ul")}`,
        `${this.$t("finOperShort")} ${this.$t("fl")}`,
        `${this.$t("finOperShort")} ${this.$t("ep")}`,
      ];

      if (this.report1 && this.report1.cases) {
        const cases = this.report1.cases;
        const events = this.report1.events;

        const datasets = [
          {
            label: this.$t("caseCount"),
            backgroundColor: "#f87979",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              cases.registration_fl,
              cases.registration_ip,
              cases.registration_ul,
              cases.auth_fl,
              cases.auth_ip,
              cases.auth_ul,
              cases.fin_oper_fl,
              cases.fin_oper_ip,
              cases.fin_oper_ul
            ]
          },
          {
            label: this.$t("eventsAmount"),
            backgroundColor: "#3D5B96",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              events.registration_fl,
              events.registration_ip,
              events.registration_ul,
              events.auth_fl,
              events.auth_ip,
              events.auth_ul,
              events.fin_oper_fl,
              events.fin_oper_ip,
              events.fin_oper_ul
            ]
          }
        ];

        return {
          labels: report1labels,
          datasets
        };
      }

      return {
        labels: report1labels,
        datasets: []
      };
    },

    report2datasets() {
      const report2labels = [
        `${this.$t("report2eventsAmount")} ${this.$t("registration")}`,
        `${this.$t("report2eventsAmount")} ${this.$t("authorization")}`,
        `${this.$t("report2eventsAmount")} ${this.$t("finOperShort")}`,
        `${this.$t("report2caseAmount")} ${this.$t("registration")}`,
        `${this.$t("report2caseAmount")} ${this.$t("authorization")}`,
        `${this.$t("report2caseAmount")} ${this.$t("finOperShort")}`
      ];
      if (this.report3 && this.report3.cases) {
        const cases = this.report3.cases;
        const events = this.report3.events;
        const caseLength = cases.length;
        const datasets = [];

        for (let i = 0; i < caseLength; i += 1) {
          let randomColor = Math.floor(Math.random() * 16777215).toString(16);
          datasets.push({
            label: cases[i].country_name,
            backgroundColor: "#" + randomColor,
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              events[i].registration,
              events[i].auth,
              events[i].fin_oper,
              cases[i].registration,
              cases[i].auth,
              cases[i].fin_oper
            ]
          });
        }

        return {
          labels: report2labels,
          datasets
        };
      }

      return {
        labels: report2labels,
        datasets: []
      };
    },

    report3datasets() {
      const report3labels = [
        "Post.kz (web)",
        "Post.kz (mobile)",
        "PayPost",
        "Way4",
        "Colvir"
      ];

      if (this.report4 && this.report4.cases) {
        const cases = this.report4.cases[0];
        const events = this.report4.events[0];

        const datasets = [
          {
            label: this.$t("caseCount"),
            backgroundColor: "#f87979",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              cases.post_kz_web,
              cases.post_kz_mobile,
              cases.pay_post,
              cases.way_4,
              cases.colvir
            ]
          },
          {
            label: this.$t("eventsAmount"),
            backgroundColor: "#3D5B96",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              events.post_kz_web,
              events.post_kz_mobile,
              events.pay_post,
              events.way_4,
              events.colvir
            ]
          }
        ];

        return {
          labels: report3labels,
          datasets
        };
      }

      return {
        labels: report3labels,
        datasets: []
      };
    },

    report4datasets() {
      const report4labels = [
             `${this.$t("registration")} ${this.$t("ul")}`,
        `${this.$t("registration")} ${this.$t("fl")}`,
        `${this.$t("registration")} ${this.$t("ep")}`,
          `${this.$t("authorization")} ${this.$t("ul")}`,
        `${this.$t("authorization")} ${this.$t("fl")}`,
        `${this.$t("authorization")} ${this.$t("ep")}`,
      ];
      if (this.report2 && this.report2.cases) {
        const cases = this.report2.cases;
        const events = this.report2.events;
        const caseLength = cases.length;
        const datasets = [];

        for (let i = 0; i < caseLength; i += 1) {
          let randomColor = Math.floor(Math.random() * 16777215).toString(16);
          datasets.push({
            label: this.currLang == 'ru' ? events[i].country_name : events[i].country_name_kz,
            backgroundColor: "#" + randomColor,
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              events[i].fl,
              events[i].ip,
              events[i].ul,
              cases[i].fl,
              cases[i].ip,
              cases[i].ul
            ]
          });
        }

        return {
          labels: report4labels,
          datasets
        };
      }

      return {
        labels: report4labels,
        datasets: []
      };
    },

    report5datasets() {
      const report5labels = [
         `${this.$t("registration")} ${this.$t("ul")}`,
        `${this.$t("registration")} ${this.$t("fl")}`,
        `${this.$t("registration")} ${this.$t("ep")}`,
          `${this.$t("authorization")} ${this.$t("ul")}`,
        `${this.$t("authorization")} ${this.$t("fl")}`,
        `${this.$t("authorization")} ${this.$t("ep")}`,
      ];

      if (this.report5) {
        const report5 = this.report5;

        const datasets = [
          {
            label: this.$t("allowed"),
            backgroundColor: "#f87979",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              report5.registration_fl_allowed,
              report5.registration_ip_allowed,
              report5.registration_ul_allowed,
              report5.auth_fl_allowed,
              report5.auth_ip_allowed,
              report5.auth_ul_allowed
            ]
          },
          {
            label: this.$t("suspended"),
            backgroundColor: "#3D5B96",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              report5.registration_fl_suspended,
              report5.registration_ip_suspended,
              report5.registration_ul_suspended,
              report5.auth_fl_suspended,
              report5.auth_ip_suspended,
              report5.auth_ul_suspended
            ]
          },
          {
            label: this.$t("forbidden"),
            backgroundColor: "pink",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              report5.registration_fl_forbidden,
              report5.registration_ip_forbidden,
              report5.registration_ul_forbidden,
              report5.auth_fl_forbidden,
              report5.auth_ip_forbidden,
              report5.auth_ul_forbidden
            ]
          }
        ];

        return {
          labels: report5labels,
          datasets
        };
      }

      return {
        labels: report5labels,
        datasets: []
      };
    },

    report6datasets() {
      const report6labels = [
              `${this.$t("registration")} ${this.$t("ul")}`,
        `${this.$t("registration")} ${this.$t("fl")}`,
        `${this.$t("registration")} ${this.$t("ep")}`,
          `${this.$t("authorization")} ${this.$t("ul")}`,
        `${this.$t("authorization")} ${this.$t("fl")}`,
        `${this.$t("authorization")} ${this.$t("ep")}`,
      ];
      if (this.report6 && this.report6.cases) {
        const cases = this.report6.cases;
        const events = this.report6.events || [];
        const caseLength = cases.length;
        const datasets = [];

        for (let i = 0; i < cases.length; i += 1) {
          let randomColor = Math.floor(Math.random() * 16777215).toString(16);
          datasets.push({
            label: cases[i].product_id,
            backgroundColor: "#" + randomColor,
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              events[i] ? events[i].fin_oper_fl : 0,
              events[i] ? events[i].fin_oper_ip : 0,
              events[i] ? events[i].fin_oper_ul : 0,
              cases[i] ? cases[i].fin_oper_fl : 0,
              cases[i] ?  cases[i].fin_oper_ip : 0,
              cases[i] ? cases[i].fin_oper_ul : 0
            ]
          });
        }

        return {
          labels: report6labels,
          datasets
        };
      }

      return {
        labels: report6labels,
        datasets: []
      };
    },

    report7datasets() {
      const report7labels = [];
      if (this.report7 && this.report7.cases) {
        const cases = this.report7.cases;
        const events = this.report7.events;
        const caseLength = cases.length;
        const datasets = [];

        for (let key in events[0]) {
          if (key !== "product_id") {
            report7labels.push(`${this.$t("report2eventsAmount")} ${key}`);
          }
        }

        for (let key in cases[0]) {
          if (key !== "product_id") {
            report7labels.push(`${this.$t("report2caseAmount")} ${key}`);
          }
        }

        for (let i = 0; i < caseLength; i += 1) {
          let randomColor = Math.floor(Math.random() * 16777215).toString(16);
          let data = [];

          for (let key in events[0]) {
            if (key !== "product_id") {
              data.push(events[i][key]);
            }
          }

          for (let key in cases[0]) {
            if (key !== "product_id") {
              data.push(cases[i][key]);
            }
          }

          datasets.push({
            label: cases[i].product_id,
            backgroundColor: "#" + randomColor,
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data
          });
        }

        return {
          labels: report7labels,
          datasets
        };
      }

      return {
        labels: report7labels,
        datasets: []
      };
    },

    report8datasets() {
      const report8labels = [];
      if (this.report8 && this.report8.cases) {
        const cases = this.report8.cases;
        const events = this.report8.events;
        const caseLength = cases.length;
        const datasets = [];

        for (let key in events[0]) {
          if (key !== "country_name" && key !== "country_name_kz" && key !== "country_name_en") {
            report8labels.push(`${this.$t("report2eventsAmount")} ${key}`);
          }
        }

        for (let key in cases[0]) {
          if (key !== "country_name" && key !== "country_name_kz" && key !== "country_name_en") {
            report8labels.push(`${this.$t("report2caseAmount")} ${key}`);
          }
        }

        for (let i = 0; i < caseLength; i += 1) {
          let randomColor = Math.floor(Math.random() * 16777215).toString(16);
          let data = [];

          for (let key in events[0]) {
            if (key !== "country_name" && key !== "country_name_kz" && key !== "country_name_en") {
              data.push(events[i][key]);
            }
          }

          for (let key in cases[0]) {
            if (key !== "country_name" && key !== "country_name_kz" && key !== "country_name_en") {
              data.push(cases[i][key]);
            }
          }

          datasets.push({
            label: this.currLang == 'ru' ? events[i].country_name : events[i].country_name_kz,
            backgroundColor: "#" + randomColor,
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data
          });
        }

        return {
          labels: report8labels,
          datasets
        };
      }

      return {
        labels: report8labels,
        datasets: []
      };
    },

    report9datasets() {
      const report9labels = [
        this.$t("allowed"),
        this.$t("suspended"),
        this.$t("forbidden")
      ];

      if (this.report9) {
        const report9 = this.report9;

        const datasets = [
          {
            label: this.$t("authorization"),
            backgroundColor: "#f87979",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              report9.auth_allowed,
              report9.auth_suspended,
              report9.auth_forbidden
            ]
          },
          {
            label: this.$t("registration"),
            backgroundColor: "#3D5B96",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              report9.registration_allowed,
              report9.registration_suspended,
              report9.registration_forbidden
            ]
          },
          {
            label: this.$t("finOperShort"),
            backgroundColor: "pink",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              report9.fin_oper_allowed,
              report9.fin_oper_suspended,
              report9.fin_oper_forbidden
            ]
          }
        ];

        return {
          labels: report9labels,
          datasets
        };
      }

      return {
        labels: report9labels,
        datasets: []
      };
    },

    allThreeSelected: function() {
      if (this.selectedReport && this.reportDateFrom && this.reportDateTo) {
        return true;
      }
      return false;
    },

    reportTitle: function() {
      if (this.selectedReport) {
        let elem = this.reportsOptions.find(x => x.key == this.selectedReport);
        return elem.key + ". " + elem.value;
      }
      return "";
    }
  }),
  watch: {
    selectedReport(newval, oldval) {
      if (this.allThreeSelected && newval !== oldval) {
        this.fetchReport();
      }
    },
    reportDateTo(newval, oldval) {
      if (this.allThreeSelected && newval !== oldval) {
        this.fetchReport();
      }
    },
    reportDateFrom(newval, oldval) {
      if (this.allThreeSelected && newval !== oldval) {
        this.fetchReport();
      }
    }
  }
};
</script>

<style>
.rowstyle {
  border-top: 1px solid;
  border-left: 1px solid;
  min-height: 43px;
}

.tablecontainer {
  background: white;
  box-shadow: rgb(0 0 0 / 15%) 0px 0px 20px;
}

.scroller::-webkit-scrollbar {
  width: 0.5px;
  height: 0.5px;
  border-radius: 25px;
}

.scroller::-webkit-scrollbar-track {
  background-color: black;
}

.scroller::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background: lightgrey;
}
</style>
