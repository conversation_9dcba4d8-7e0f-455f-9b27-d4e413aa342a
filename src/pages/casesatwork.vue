<template>
  <q-page class="q-pa-lg" style="height: 95vh">
    <div style="height: -webkit-fill-available " class="column">
      <div class="row items-center q-mb-md col-1">
        <span class="q-ml-sm"> {{ $t("filter") }}: </span>
        <q-select
          class="q-ml-md"
          outlined
          dense
          clearable
          v-model="selectedFilter"
          :options="filterOptions"
          style="min-width: 250px;"
        />
        <span
          v-if="
            selectedFilter &&
              (selectedFilter == 'Пользователь' ||
                selectedFilter == 'Статус Кейса' ||
                selectedFilter == 'Статус События')
          "
          class="q-ml-md"
        >
          {{$t('value')}}:
        </span>
        <q-select
          class="q-ml-md"
          outlined
          dense
          v-if="selectedFilter && selectedFilter == 'Пользователь'"
          v-model="selectedValue"
          :options="users"
          option-value="id"
          :option-label="
            opt =>
              Object(opt) === opt && 'first_name' in opt
                ? opt.first_name + ' ' + opt.last_name
                : null
          "
          emit-value
          map-options
          style="min-width: 250px;"
        />
        <q-select
          class="q-ml-md"
          outlined
          dense
          v-if="selectedFilter && selectedFilter == 'Статус Кейса'"
          v-model="selectedValue"
          :options="statuses"
          option-value="key"
          option-label="value"
          emit-value
          map-options
          style="min-width: 250px;"
        />

        <q-select
          class="q-ml-md"
          outlined
          dense
          v-if="selectedFilter && selectedFilter == 'Статус События'"
          v-model="selectedValue"
          :options="caseStatusesSystem"
          option-value="key"
          option-label="value"
          emit-value
          map-options
          style="min-width: 250px;"
        />
      </div>

      <div
        class="row q-pb-md col-6"
        ref="parentcontainer"
        style="width: 100%; height: fit-content"
      >
        <vue-draggable-resizable
          class-name="cardcontainer"
          :w="parentWidth"
          :h="parentHeight"
          style="max-height: -webkit-fill-available; "
          :parent="true"
        >
          <q-splitter v-model="splitterModel" style="height: 100%; ">
            <template v-slot:before>
              <q-tabs v-model="workingTab" vertical class="text-teal">
                <q-tab name="mails" :label="$t('allCases')" />
                <q-tab name="alarms" :label="$t('casesStopped')" />
              </q-tabs>
            </template>

            <template v-slot:after>
              <q-tab-panels
                v-model="workingTab"
                animated
                swipeable
                vertical
                transition-prev="jump-up"
                transition-next="jump-up"
              >
                <q-tab-panel name="mails" class="q-pa-none">
                  <q-table
                    :tabletitle="$t('cases')"
                    :data="cases"
                    :columns="columns"
                    row-key="name"
                    style="height: 100%"
                    :filter="filter"
                    :no-data-label="$t('recordNotFound')"
                    :no-results-label="$t('recordNotFound')"
                    :pagination.sync="pagination"
                    :grid="mode == 'grid'"
                    :loading="loading"
                    @request="onRequest"
                    binary-state-sort
                  >
                    <template v-slot:top-right>
                      <q-input
                        outlined
                        dense
                        debounce="300"
                        v-model="filter"
                        :placeholder="$t('search')"
                      >
                        <template v-slot:append>
                          <q-icon name="search" />
                        </template>
                      </q-input>
                    </template>

                    <template v-slot:body="props">
                      <q-tr :props="props">
                        <q-td key="id" :props="props">
                          <span
                            :style="{
                              cursor: 'pointer',
                              color:
                                +props.row.finish_assigned_date[0] !== 0 &&
                                +props.row.decision == 2
                                  ? dateDiff(
                                      props.row.finish_assigned_date,
                                      new Date()
                                    ) < 0
                                    ? 'red'
                                    : ''
                                  : ''
                            }"
                            @click="currentlySelectedCase = props.row.id"
                            class="case_text"
                          >
                            {{ props.row.id }}
                          </span>
                        </q-td>

                        <q-td key="creation_date" :props="props">
                          {{ formattedDate(props.row.creation_date) }}
                        </q-td>

                        <q-td key="finish_due_date" :props="props">
                          {{
                            +props.row.finish_due_date[0] !== 0
                              ? formattedDate(props.row.finish_due_date)
                              : ""
                          }}
                        </q-td>

                        <q-td key="finish_assigned_date" :props="props">
                          {{
                            +props.row.finish_assigned_date[0] !== 0
                              ? formattedDate(props.row.finish_assigned_date)
                              : ""
                          }}
                        </q-td>

                        <q-td key="score" :props="props">
                          <q-icon
                            name="warning"
                            :class="
                              props.row.score <= 40
                                ? 'text-orange'
                                : props.row.score > 40 && props.row.score <= 60
                                ? 'text-blue'
                                : 'text-red'
                            "
                            style="font-size: 13px;"
                          />
                          {{ props.row.score || 0 }}
                        </q-td>

                        <q-td key="priority" :props="props">
                          {{
                            props.row.score <= 40
                              ? $t("low")
                              : props.row.score > 40 && props.row.score <= 60
                              ? $t("middle")
                              : $t("high")
                          }}
                        </q-td>

                        <q-td key="assigned_user" :props="props">
                          {{
                            props.row.assigned_user
                              ? printUser(props.row.assigned_user)
                              : ""
                          }}
                        </q-td>

                        <q-td key="decision" :props="props">
                          {{
                            props.row.decision && statuses.length
                              ? convertDecision(
                                  statuses.find(
                                    x => +x.key == props.row.decision
                                  ).value
                                )
                              : props.row.event_status && caseStatusesSystem
                              ? convertDecision(
                                  caseStatusesSystem.find(
                                    x => +x.key == props.row.event_status
                                  ).value
                                )
                              : ""
                          }}
                        </q-td>

                        <q-td key="is_archive" :props="props">
                          <q-icon
                            name="done"
                            color="green"
                            v-if="props.row.is_archive"
                          />
                        </q-td>
                      </q-tr>
                    </template>
                  </q-table>
                </q-tab-panel>

                <q-tab-panel name="alarms" class="q-pa-none">
                  <q-table
                    :title="$t('casesStopped')"
                    :data="casesWorkFiltered"
                    :columns="columns"
                    row-key="name"
                    style="height: 100%"
                    :filter="filter2"
                    :no-data-label="$t('recordNotFound')"
                    :no-results-label="$t('recordNotFound')"
                    :pagination.sync="pagination2"
                    :grid="mode == 'grid'"
                    :loading="loading2"
                    binary-state-sort
                  >
                    <template v-slot:top-right>
                      <!-- <q-icon name="refresh" class="q-ml-sm" size="sm" @click="getAllCasesWorkInner">
            <q-tooltip :disable="$q.platform.is.mobile" v-close-popup>
              Обновить
            </q-tooltip>
          </q-icon> -->

                      <q-input
                        outlined
                        dense
                        debounce="300"
                        v-model="filter2"
                        :placeholder="$t('search')"
                      >
                        <template v-slot:append>
                          <q-icon name="search" />
                        </template>
                      </q-input>
                    </template>

                    <template v-slot:body="props">
                      <q-tr :props="props">
                        <q-td key="id" :props="props">
                          <span
                            :style="{
                              cursor: 'pointer',
                              color:
                                +props.row.finish_assigned_date[0] !== 0 &&
                                +props.row.decision == 2
                                  ? dateDiff(
                                      props.row.finish_assigned_date,
                                      new Date()
                                    ) < 0
                                    ? 'red'
                                    : ''
                                  : ''
                            }"
                            @click="currentlySelectedCase = props.row.id"
                            class="case_text"
                          >
                            {{ props.row.id }}
                          </span>
                        </q-td>

                        <q-td key="creation_date" :props="props">
                          {{ formattedDate(props.row.creation_date) }}
                        </q-td>

                        <q-td key="finish_due_date" :props="props">
                          {{
                            +props.row.finish_due_date[0] !== 0
                              ? formattedDate(props.row.finish_due_date)
                              : ""
                          }}
                        </q-td>

                        <q-td key="finish_assigned_date" :props="props">
                          {{
                            +props.row.finish_assigned_date[0] !== 0
                              ? formattedDate(props.row.finish_assigned_date)
                              : ""
                          }}
                        </q-td>

                        <q-td key="score" :props="props">
                          <q-icon
                            name="warning"
                            :class="
                              props.row.score <= 40
                                ? 'text-orange'
                                : props.row.score > 40 && props.row.score <= 60
                                ? 'text-blue'
                                : 'text-red'
                            "
                            style="font-size: 13px;"
                          />
                          {{ props.row.score || 0 }}
                        </q-td>

                        <q-td key="priority" :props="props">
                          {{
                            props.row.score <= 40
                              ? $t("low")
                              : props.row.score > 40 && props.row.score <= 60
                              ? $t("middle")
                              : $t("high")
                          }}
                        </q-td>

                        <q-td key="assigned_user" :props="props">
                          {{
                            props.row.assigned_user
                              ? printUser(props.row.assigned_user)
                              : ""
                          }}
                        </q-td>

                        <q-td key="decision" :props="props">
                          {{
                            props.row.event_status && caseStatusesSystem.length
                              ? convertDecision(
                                  caseStatusesSystem.find(
                                    x => +x.key == props.row.event_status
                                  ).value
                                )
                              : ""
                          }}
                        </q-td>

                        <q-td key="is_archive" :props="props">
                          <q-icon
                            name="done"
                            color="green"
                            v-if="props.row.is_archive"
                          />
                        </q-td>
                      </q-tr>
                    </template>
                  </q-table>
                </q-tab-panel>
              </q-tab-panels>
            </template>
          </q-splitter>
        </vue-draggable-resizable>

        <vue-draggable-resizable
          :w="parentWidth"
          :h="parentHeight"
          class-name="cardcontainer  q-ml-md "
          style="max-height: -webkit-fill-available; "
          :parent="true"
        >
          <Bar
            :chartData="report9datasets"
            :options="report1options"
            :styles="{ height: '100%' }"
          />
        </vue-draggable-resizable>
      </div>

      <div
        class="cardcontainer column col q-pa-none"
        v-if="currentlySelectedCase"
        style="width: 100%; background: #f0f4fd"
      >
        <CaseDetails
          :returnbuttonneeded="false"
          :caseidprop="currentlySelectedCase"
        />
      </div>

      <!-- <router-view></router-view> -->
    </div>
  </q-page>
</template>

<script>
import { exportFile, date } from "quasar";
import { mapState, mapActions } from "vuex";
import datemixin from "../mixins/datemixin";
import Bar from "../components/BarChart";
import VueDraggableResizable from "vue-draggable-resizable";
import "vue-draggable-resizable/dist/VueDraggableResizable.css";

export default {
  mixins: [datemixin],
  components: {
    CustomTable: () => import("../components/CustomTable"),
    CaseDetails: () => import("../pages/case_details"),
    Bar: Bar,
    VueDraggableResizable: VueDraggableResizable
  },
  data() {
    return {
      parentWidth: 1,
      parentHeight: 100,
      graph: {
        x: 0,
        y: 0,
        w: 1000,
        h: 1000
      },
      report1options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          xAxes: [
            {
              stacked: true,
              categoryPercentage: 0.5,
              barPercentage: 1
            }
          ],
          yAxes: [
            {
              stacked: true
            }
          ]
        }
      },
      mode: "list",
      selectedValue: "",
      currentlySelectedCase: 0,
      location: window.location,
      selectedFilter: "",
      filterOptions: ["Пользователь", "Статус Кейса"],
      workingTab: "mails",
      splitterModel: 20,
      pagination: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5,
        rowsNumber: 200
      },
      loading: false,
      loading2: false,
      filter: "",
      filter2: "",
      pagination2: {
        sortBy: "id",
        descending: false,
        page: 1,
        rowsPerPage: 5
      }
    };
  },

  created() {
    let start_date = this.formatDateToDB(
      new Date("December 17, 1995 03:24:00")
    );
    let end_date = this.formatDateToDB(new Date());

    setTimeout(() => {
      let elem = this.$refs.parentcontainer;
      this.parentWidth = elem.parentElement.clientWidth / 2 - 20;
      this.parentHeight = elem.parentElement.clientHeight - 460;
    });

    this.getReport9({ start_date, end_date })
      .then()
      .catch(err => {
        this.$q.notify({
          message: err.error,
          color: "negative"
        });
      });

    this.onRequest(
      { pagination: this.pagination, filter: this.filter },
      "id",
      ""
    );

    if (!this.casesWork.length) {
      this.getAllCasesWorkInner();
    }

    if (!this.users.length) {
      this.getAllUsers()
        .then()
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }

    if (!this.statuses.length) {
      this.getCaseStatuses({ page: 1, per_page: 5 })
        .then()
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    }
    if (!this.caseStatusesSystem.length) {
      this.getCaseStatusesSystem({ page: 1, per_page: 10 })
        .then(res => {
          console.log(this.caseStatusesSystem);
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    }

    //this.openInNewTab([`cases?lang=${this.currLang}`,`reportsall4onepage?lang=${this.currLang}`]);
  },

  watch: {
    selectedValue: function(newval, oldval) {
      if (newval !== oldval && newval && this.selectedFilter) {
        const filter = this.filter;
        const pagination = this.pagination;
        if (this.selectedFilter == "Статус Кейса") {
          if (this.workingTab == "mails") {
            this.onRequest(
              { filter, pagination },
              "decision",
              +this.selectedValue
            );
          }
        }

        if (this.selectedFilter == "Пользователь") {
          if (this.workingTab == "mails") {
            this.onRequest(
              { filter, pagination },
              "assigned_user",
              +this.selectedValue
            );
          }
        }
      }
    },

    selectedFilter: function(newval, oldval) {
      if (newval !== oldval) {
        this.selectedValue = "";
      }
      if (!newval) {
        const filter = this.filter;
        const pagination = this.pagination;
        this.onRequest({ filter, pagination });
      }
    }
  },

  computed: mapState({
    cases: state => state.cases.cases,
    casesWork: state => state.cases.casesWork,
    statuses: state => state.admin.statuses,
    caseStatusesSystem: state => state.admin.statusessystem,
    users: state => state.admin.users,
    report9: state => state.admin.report9,

    columns() {
      return [
        {
          name: "id",
          align: "left",
          label: this.$t("caseId"),
          field: "id",
          sortable: true
        },
        {
          name: "creation_date",
          required: true,
          label: this.$t("casedateecreated"),
          align: "left",
          field: row => row.creation_date,
          sortable: true
        },
        {
          name: "finish_due_date",
          align: "left",
          label: this.$t("casedateclosed"),
          field: "finish_due_date",
          sortable: true
        },
        {
          name: "finish_assigned_date",
          align: "left",
          label: this.$t("caseduedate"),
          field: "finish_assigned_date",
          sortable: true
        },
        {
          name: "score",
          align: "left",
          label: this.$t("score"),
          field: row => row.score,
          sortable: true
        },
        {
          name: "priority",
          align: "left",
          label: this.$t("riskLevel"),
          field: "priority",
          sortable: true
        },
        {
          name: "assigned_user",
          align: "left",
          label: this.$t("user"),
          field: "assigned_user",
          sortable: true
        },
        {
          name: "decision",
          align: "left",
          label: this.$t("status"),
          field: "decision",
          sortable: true
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ];
    },

    casesWorkFiltered() {
      let filtered = this.casesWork.filter(c => {
        return c.event_status == 2;
      });

      if (this.selectedFilter == "Статус") {
        filtered = filtered.filter(c => {
          return c.decision == this.selectedValue;
        });
      }

      if (this.selectedFilter == "Пользователь") {
        filtered = filtered.filter(c => {
          return c.assigned_user == this.selectedValue;
        });
      }

      return filtered;
    },
    report9datasets() {
      const report9labels = [this.$t('allowed'), this.$t('suspended'), this.$t('forbidden')];

      if (this.report9) {
        const report9 = this.report9;

        const datasets = [
          {
            label: this.$t('authorization'),
            backgroundColor: "#f87979",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              report9.auth_allowed,
              report9.auth_suspended,
              report9.auth_forbidden
            ]
          },
          {
            label: this.$t('registration'),
            backgroundColor: "#3D5B96",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              report9.registration_allowed,
              report9.registration_suspended,
              report9.registration_forbidden
            ]
          },
          {
            label: this.$t('finOperShort'),
            backgroundColor: "pink",
            pointBackgroundColor: "white",
            borderWidth: 1,
            pointBorderColor: "#249EBF",
            data: [
              report9.fin_oper_allowed,
              report9.fin_oper_suspended,
              report9.fin_oper_forbidden
            ]
          }
        ];

        return {
          labels: report9labels,
          datasets
        };
      }

      return {
        labels: report9labels,
        datasets: []
      };
    }
  }),

  methods: {
    ...mapActions({
      getAllCases: "cases/getAllCases",
      getAllCasesWork: "cases/getAllCasesWork",
      getCaseStatuses: "admin/getCaseStatuses",
      getAllUsers: "admin/getAllUsers",
      getCaseStatusesSystem: "admin/getCaseStatusesSystem",
      getReport9: "admin/getReport9"
    }),

    openInNewTab(urls) {
      urls.forEach(url => {
        window.open(url); 
      })
    },

    convertDecision(val) {
      if (val == "Разрешено") {
        return this.$t("allowed");
      } else if (val == "Приостановлено") {
        return this.$t("suspended");
      } else if (val == "Запрещено") {
        return this.$t("forbidden");
      } else if (val == "На анализе") {
        return this.$t("onanalys");
      } else {
        return this.$t("unabletocheck");
      }
    },

    getAllCasesWorkInner() {
      this.loading2 = true;
      this.getAllCasesWork({
        page: 1,
        per_page: 99999999999
      })
        .then(res => {
          this.loading2 = false;
        })
        .catch(err => {
          this.loading2 = false;
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    },

    formattedDate(val) {
      return date.formatDate(val, "YYYY-MM-DD HH:mm:ss");
    },

    printUser(user_id) {
      if (this.users.length) {
        let elem = this.users.find(x => x.id == user_id);
        return elem.first_name + " " + elem.last_name;
      } else {
        return "";
      }
    },

    async onRequest(props, filter_field = "id", filter_value) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;

      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getAllCases({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: filter_field,
        filter_values: filter_value ? filter_value : filter
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    }
  }
};
</script>
<style>
.q-chip__content {
  display: block;
  text-align: center;
}
.cardcontainer {
  border-radius: 16px;
  background: white;
  box-shadow: 0 5px 10px rgba(154, 160, 185, 0.05),
    0 15px 40px rgba(166, 173, 201, 0.2);
}
.case_text {
  color: #0e64f2;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 22px;
  /* identical to box height, or 157% */

  text-decoration-line: underline;
}

.q-table tbody td {
  font-size: 13px;
}
</style>
