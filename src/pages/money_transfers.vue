<template>
  <q-page class="q-pa-lg">
    <CustomTable
      :data="money_transfers"
      :columns="columns"
      :tabletitle="$t('money_transfers')"
      :isDeleteAvailable="false"
      :isAddAvailable="false"
      :isEditAvailable="false"
      :pagination.sync="pagination"
      @onRequest="onRequest"
      :loading="loading"
      :filter.sync="filter"
    >
      <template v-slot:searchfield="{ props }">
        <q-input
          outlined
          dense
          debounce="300"
          v-model="filter"
          :placeholder="$t('search')"
        >
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
        <span class="q-ml-lg col-1 q-mr-sm"> {{ $t('period') }}: </span>
        <q-input outlined dense type="date" v-model="startDate" />
        <q-input outlined dense type="date" v-model="endDate" />
        <q-btn
          flat
          round
          dense
          icon="download"
          :disable="!startDate || !endDate"
          color="black"
          @click="exportToExcel"
        />
      </template>

      <template v-slot:oper_date_time="{ props }">
        {{ formattedDate(props.row.oper_date_time) }}
      </template>
      <template v-slot:transaction_status="{ props }">
        <q-icon
          v-if="props.row.transaction_status === 1"
          name="check_circle"
          size="20px"
          color="green"
        />
        <q-icon
          v-else-if="props.row.transaction_status === 3"
          name="cancel"
          size="20px"
          color="red"
        />
        <span v-else>{{ props.row.transaction_status }}</span>
      </template>
    </CustomTable>
    <router-view></router-view>
  </q-page>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import datemixin from '../mixins/datemixin';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
export default {
  mixins: [datemixin],
  components: {
    CustomTable: () => import('../components/CustomTable'),
  },
  data() {
    return {
      mode: 'list',
      selectedValue: '',
      selectedFilter: '',

      pagination: {
        sortBy: 'oper_date_time',
        descending: true,
        page: 1,
        rowsPerPage: 10,
        rowsNumber: 200,
      },
      loading: false,
      filter: '',
      startDate: '',
      endDate: '',
    };
  },

  created() {},

  watch: {
    selectedFilter: function(newval, oldval) {
      if (newval !== oldval) {
        this.selectedValue = '';
      }
      if (!newval) {
        const filter = this.filter;
        const pagination = this.pagination;
        this.onRequest({ filter, pagination });
      }
    },
    startDate(newVal) {
      this.onRequest({ pagination: this.pagination, filter: this.filter });
    },
    endDate(newVal) {
      this.onRequest({ pagination: this.pagination, filter: this.filter });
    },
  },

  computed: mapState({
    money_transfers: (state) => state.events.money_transfers,
    columns() {
      return [
        { name: 'id', label: 'Номер транзакции', field: 'id', sortable: true },
        {
          name: 'oper_date_time',
          align: 'left',
          label: 'Дата',
          field: 'oper_date_time',
          sortable: true,
        },
        {
          name: 'sender_iin',
          label: 'ИИН отправителя',
          align: 'left',
          field: 'sender_iin',
          sortable: true,
        },
        {
          name: 'recipient_iin',
          align: 'left',
          label: 'ИИН получателя',
          field: 'recipient_iin',
          sortable: true,
        },
        {
          name: 'amount_kzt',
          align: 'left',
          label: 'Сумма (KZT)',
          field: 'amount_kzt',
          sortable: true,
        },

        {
          name: 'sender_card_number',
          label: 'Карта отправителя',
          align: 'left',
          field: 'sender_card_number',
          sortable: true,
        },
        {
          name: 'recip_card_number',
          align: 'left',
          label: 'Карта получателя',
          field: 'recip_card_number',
          sortable: true,
        },
        {
          name: 'transaction_status',
          align: 'left',
          label: 'Статус',
          field: 'transaction_status',
          sortable: true,
        },
        {
          name: 'actions',
          align: 'left',
          label: '',
          field: 'actions',
          sortable: false,
        },
      ];
    },
  }),

  methods: {
    ...mapActions({
      getAllMoneyTransfers: 'events/getAllMoneyTransfers',
      getMoneyTransfersExportExcel: "events/getMoneyTransfersExportExcel"
    }),

    formattedDate(val) {
      return dayjs(val)
        .utc()
        .format('YYYY-MM-DD HH:mm:ss');
    },

    async exportToExcel() {
      try {
        if (!this.startDate || !this.endDate) {
          this.$q.notify({
            message: 'Пожалуйста, заполните обе даты.',
            color: 'negative',
          });
          return;
        }
        console.log('startDate ', this.startDate, this.endDate)
        const response = await this.getMoneyTransfersExportExcel( {
          start_date: this.startDate,
          end_date: this.endDate,
        });

        if (response.status === 200) {
          const fileName = `money_transfers_${this.startDate}_${this.endDate}.xlsx`;
          const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = fileName;
          link.click();
        }
      } catch (error) {
        this.$q.notify({
          message: error.message || 'Ошибка при выгрузке данных.',
          color: 'negative',
        });
      }
    },

    async onRequest(
      props,
      filter_field = ['tefo.id', 'tp.iin', 'tp2.iin'],
      filter_value
    ) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending,
      } = props.pagination;
      const filter = props.filter;
      let start_date = this.startDate;
      let end_date = this.endDate;
      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getAllMoneyTransfers({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? 'desc' : 'asc',
        filter_fields: filter_field,
        filter_values: filter_value
          ? [filter_value, filter_value, filter_value]
          : [filter, filter, filter],
        start_date: start_date,
        end_date: end_date,
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    },
  },
};
</script>
<style>
.q-table tbody td {
  font-size: 13px;
}
</style>
