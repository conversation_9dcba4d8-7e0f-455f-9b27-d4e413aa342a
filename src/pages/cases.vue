<template>
  <q-page class="q-pa-lg">



    <CustomTable
      :data="casesDetailed"
      :columns="columns"
      :tabletitle="$t('cases')"
      :isDeleteAvailable="false"
      :isAddAvailable="false"
      :isEditAvailable="false"
      :pagination.sync="pagination"
      @onRequest="onRequest"
      @onRefresh="({filter, pagination})"
      :loading="loading"
      :filter.sync="filter"
    >
      <template v-slot:searchfield="{props}">
        <q-input
          outlined
          dense
          debounce="300"
          v-model="filter"
          :placeholder="$t('search')"
        >
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>

        <span class="q-ml-lg col-1 q-mr-sm"> {{ $t("period") }}: </span>
        <q-input
          outlined
          dense
          type="date"
          v-model="startDate"
        />
        <q-input
          outlined
          dense
          type="date"
          v-model="endDate"
        />
        <q-btn
          flat
          round
          dense
          icon="download"
          :disable="!startDate || !endDate"
          color="black"
          @click="exportToExcel"
        />
      </template>

      <template v-slot:score="{ props }">
        <q-icon
          name="warning"
          :class="
            props.row.score <= 40
              ? 'text-orange'
              : props.row.score > 40 && props.row.score <= 60
              ? 'text-blue'
              : 'text-red'
          "
          style="font-size: 13px;"
        />
        {{ props.row.score || 0 }}
      </template>

      <template v-slot:creation_date="{ props }">
        {{ formattedDate(props.row.creation_date) }}
      </template>

      <template v-slot:amount_kzt="{ props }">
        {{ props.row.amount_kzt  }}
      </template>

      <template v-slot:full_name="{ props }">
        {{ props.row.full_name  }}
      </template>


      <template v-slot:assigned_user="{ props }">
        {{ console.log('assigned_user value:', props.row.assigned_user) }}
        {{
          props.row.assigned_user ? printUser(props.row.assigned_user) : ""
        }}
      </template>


      <template v-slot:id="{ props }">

        <span
           :style="{'cursor': 'pointer', color: +props.row.finish_assigned_date[0] !== 0 && +props.row.decision == 2 ? (dateDiff(props.row.finish_assigned_date, new Date()) < 0 ? 'red' : '') : ''}"
          @click="$router.push({ path: `case_details/${props.row.id}` })"
          class="case_text"
        >
          {{ props.row.id }}
        </span>
      </template>


    </CustomTable>
    <router-view></router-view>
  </q-page>
</template>

<script>
import { exportFile, date } from "quasar";
import { mapState, mapActions } from "vuex";
import datemixin from "../mixins/datemixin"

export default {
  mixins: [datemixin],
  components: {
    CustomTable: () => import("../components/CustomTable")
  },
  data() {
    return {
      mode: "list",
      selectedValue: "",
      selectedFilter: "",

      pagination: {
        sortBy: "creation_date",
        descending: true,
        page: Number(localStorage.getItem("lastPage")) || 1,
        rowsPerPage: 10,
        rowsNumber: 200
      },
      loading: false,
      filter: "",
      startDate: "",
      endDate: ""
    };
  },

  created() {
    if (!this.users.length) {
      this.getAllUsers()
        .then()
        .catch(err => {
          this.$q.notify({
            message: err.message,
            color: "negative"
          });
        });
    }

     if (!this.statuses.length) {
        this.getCaseStatuses({ page: 1, per_page: 5 })
          .then()
          .catch(err => {
            this.$q.notify({
              message: err.error,
              color: "negative"
            });
          });
      }
      if (!this.caseStatusesSystem.length) {
      this.getCaseStatusesSystem({ page: 1, per_page: 10 })
        .then(res => {
          // console.log(this.caseStatusesSystem)
        })
        .catch(err => {
          this.$q.notify({
            message: err.error,
            color: "negative"
          });
        });
    }
  },

  watch: {
    "pagination.page": function (newPage) {
      localStorage.setItem("lastPage", newPage);
    },
    selectedValue: function (newval, oldval) {
      if (newval !== oldval && newval && this.selectedFilter) {
        const filter = this.filter;
          const pagination = this.pagination
        if (this.selectedFilter == 'Статус') {
          this.onRequest({filter, pagination}, "decision", +this.selectedValue)
        } else {
          this.onRequest({filter, pagination}, "assigned_user", +this.selectedValue)
        }
      }
    },

    selectedFilter: function (newval, oldval) {
      if (newval !== oldval) {
        this.selectedValue = ""
      }
      if (!newval) {
       const filter = this.filter;
       const pagination = this.pagination
       this.onRequest({filter,pagination})
      }
    },
      startDate(newVal) {
          this.onRequest({ pagination: this.pagination, filter: this.filter });
      },
      endDate(newVal) {
          this.onRequest({ pagination: this.pagination, filter: this.filter });
      }
  },

  computed: mapState({
    exportReport: state => state.cases.exportReport,
    casesDetailed: state => state.cases.casesDetailed,
    statuses: state => state.admin.statuses,
    caseStatusesSystem: state => state.admin.statusessystem,
    users: state => state.admin.users,
    columns() {
      return [
        {
          name: "id",
          align: "left",
          label: this.$t('caseId'),
          field: "id",
          sortable: true
        },
        {
          name: "creation_date",
          required: true,
          label: this.$t('casedateecreated'),
          align: "left",
          field: "creation_date",
          sortable: true
        },
        {
          name: "iin",
          align: "left",
          label: this.$t('iin'),
          field:"iin",
          sortable: true
        },
        {
          name: "full_name",
          align: "left",
          label: this.$t('full_name'),
          field:"full_name",
          sortable: true
        },
        {
          name: "score",
          align: "left",
          label: this.$t('score'),
          field:"score",
          sortable: true
        },
        {
          name: "product_id",
          align: "left",
          label: this.$t('product_id'),
          field:"product_id",
          sortable: true
        },
        {
          name: "amount_kzt",
          align: "left",
          label: this.$t('amount_kzt'),
          field:"amount_kzt",
          sortable: true
        },
        {
          name: "assigned_user",
          align: "left",
          label: this.$t('user'),
          field: "assigned_user",
          sortable: true
        },
        {
          name: "decision",
          align: "left",
          label: this.$t('status'),
          field: "decision",
          sortable: true
        },
        {
          name: "actions",
          label: "",
          field: "actions",
          sortable: false
        }
      ]
    }
  }),

  methods: {
    ...mapActions({
      getAllCasesDetailed: "cases/getAllCasesDetailed",
      getCaseStatuses: "admin/getCaseStatuses",
      getAllUsers: "admin/getAllUsers",
      getCaseStatusesSystem: "admin/getCaseStatusesSystem",
      getExportExcel: "cases/getExportExcel"
    }),
    convertDecision(val) {
      if (val == 'Разрешено') {
        return this.$t('allowed')
      } else if (val == 'Приостановлено') {
        return this.$t('suspended')
      } else if (val == 'Запрещено') {
        return this.$t('forbidden')
      } else if (val == 'На анализе') {
        return this.$t('onanalys')
      } else {
        return this.$t('unabletocheck')
      }
    },
    formattedDate(val) {
      return date.formatDate(val, "YYYY-MM-DD HH:mm:ss");
    },

    async exportToExcel() {
      try {
        if (!this.startDate || !this.endDate) {
          this.$q.notify({
            message: 'Пожалуйста, заполните обе даты.',
            color: 'negative',
          });
          return;
        }
        console.log('startDate ', this.startDate, this.endDate)
        const response = await this.getExportExcel( {
            start_date: this.startDate,
            end_date: this.endDate,
        });

        if (response.status === 200) {
          const fileName = `cases_${this.startDate}_${this.endDate}.xlsx`;
          const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = fileName;
          link.click();
        }
      } catch (error) {
        this.$q.notify({
          message: error.message || 'Ошибка при выгрузке данных.',
          color: 'negative',
        });
      }
    },

    printUser(user_id) {
      console.log('printUser called with user_id:', user_id);
      console.log('users array length:', this.users.length);
      console.log('users array:', this.users);

      if (this.users.length) {
        let elem = this.users.find(x => x.id == user_id)
        console.log('found user:', elem);
        return elem ? (elem.first_name + " " + elem.last_name) : ""
      } else {
        return ""
      }
    },
    async onRequest(props, filter_field, filter_value) {
      const {
        page,
        rowsPerPage,
        rowsNumber,
        sortBy,
        descending
      } = props.pagination;
      const filter = props.filter;
      filter_field = ["c.id","ep.iin"];
      let start_date = this.startDate;
      let end_date = this.endDate;
      this.loading = true;

      // get all rows if "All" (0) is selected
      const fetchCount =
        rowsPerPage === 0 ? this.pagination.rowsNumber : rowsPerPage;

      // calculate starting row of data
      const startRow = (page - 1) * rowsPerPage;

      this.pagination.rowsNumber = await this.getAllCasesDetailed({
        page: page,
        per_page: rowsPerPage,
        sort_field: sortBy,
        sort_order: descending ? "desc" : "asc",
        filter_fields: filter_field,
        filter_values: filter_value
            ? [filter_value, filter_value]
            : [filter, filter],
        start_date: start_date,
        end_date: end_date
      });

      // don't forget to update local pagination object
      this.pagination.page = page;
      this.pagination.rowsPerPage = rowsPerPage;
      this.pagination.sortBy = sortBy;
      this.pagination.descending = descending;
      this.loading = false;
    }
  }
};
</script>
<style>
.q-chip__content {
  display: block;
  text-align: center;
}
.case_text {
  color: #0e64f2;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 22px;
  /* identical to box height, or 157% */

  text-decoration-line: underline;
}

.q-table tbody td {
  font-size: 13px;
}
</style>
